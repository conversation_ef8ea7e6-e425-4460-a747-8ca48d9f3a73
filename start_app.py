#!/usr/bin/env python3
"""
QR ID Desktop Application Launcher
Simple launcher script for the local desktop version of the QR ID printing application.
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = {
        'flask': 'flask',
        'pandas': 'pandas',
        'pillow': 'PIL',
        'qrcode': 'qrcode',
        'cryptography': 'cryptography',
        'psutil': 'psutil',
        'reportlab': 'reportlab',
        'python-dotenv': 'dotenv'
    }

    missing_packages = []
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 To install missing packages, run:")
        print(f"   pip install {' '.join(missing_packages)}")
        print("\n   Or install all requirements:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def setup_directories():
    """Create necessary directories for local operation"""
    base_dir = Path(__file__).parent
    directories = [
        base_dir / "static" / "qr_codes",
        base_dir / "static" / "id_templates", 
        base_dir / "participant_list",
        base_dir / "static" / "previews"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"✓ Directory ready: {directory.name}")

def create_env_file():
    """Create .env file with local configuration if it doesn't exist"""
    env_file = Path(__file__).parent / ".env"
    
    if not env_file.exists():
        env_content = """# QR ID Desktop Application Configuration
# Local desktop application settings

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-change-this-for-production

# Memory Settings (generous for local desktop use)
MEMORY_LIMIT_MB=1024

# Feature Toggles
ENABLE_SCHEDULER=true
ENABLE_BACKGROUND_QR=true

# Email Configuration (optional - only needed for email features)
# Uncomment and configure if you want to send QR codes via email
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USE_TLS=true
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password
# MAIL_DEFAULT_SENDER=<EMAIL>

# Local Application Settings
PYTHONUNBUFFERED=1
"""
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✓ Created .env configuration file")
    else:
        print("✓ Configuration file exists")

def open_browser_delayed():
    """Open browser after Flask starts"""
    time.sleep(2)  # Wait for Flask to fully start
    webbrowser.open('http://localhost:5000')

def main():
    """Main launcher function"""
    print("=" * 60)
    print("🚀 QR ID Desktop Application Launcher")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return
    
    print("✓ Python version compatible")
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        input("Press Enter to exit...")
        return
    
    print("✓ All dependencies available")
    
    # Setup directories
    print("📁 Setting up directories...")
    setup_directories()
    
    # Create .env file
    print("⚙️  Setting up configuration...")
    create_env_file()
    
    print("\n" + "=" * 60)
    print("🎉 Starting QR ID Desktop Application...")
    print("=" * 60)
    print("📱 QR Code Generation & ID Management System")
    print("🌐 Opening in your default browser...")
    print("🔗 Local URL: http://localhost:5000")
    print("=" * 60)
    print("✨ Features Available:")
    print("   • QR Code Generation & Scanning")
    print("   • ID Card Printing")
    print("   • CSV Data Import")
    print("   • Email QR Codes (requires internet)")
    print("   • Local File Management")
    print("=" * 60)
    print("💡 To stop the application, press Ctrl+C")
    print("=" * 60)
    
    # Start browser opening in background
    browser_thread = threading.Thread(target=open_browser_delayed, daemon=True)
    browser_thread.start()
    
    # Start the Flask application
    try:
        from app import app
        app.run(
            host='127.0.0.1',  # Local only
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False  # Disable reloader for desktop use
        )
    except KeyboardInterrupt:
        print("\n\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        print("💡 Try running: python app.py")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
