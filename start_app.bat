@echo off
REM QR ID Desktop Application Launcher for Windows
REM Double-click this file to start the application

title QR ID Desktop Application

echo ============================================================
echo    QR ID Desktop Application Launcher
echo ============================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found - checking version...
python -c "import sys; exit(0 if sys.version_info >= (3,8) else 1)"
if errorlevel 1 (
    echo ERROR: Python 3.8 or higher is required
    python --version
    pause
    exit /b 1
)

echo Python version OK
echo.

REM Check if requirements are installed
echo Checking dependencies...
python -c "import flask, pandas, PIL, qrcode, cryptography, psutil, reportlab" >nul 2>&1
if errorlevel 1 (
    echo Some dependencies are missing. Installing...
    echo.
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        echo Please run: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo Dependencies OK
echo.

REM Start the application
echo Starting QR ID Desktop Application...
echo.
echo The application will open in your default browser
echo To stop the application, close this window or press Ctrl+C
echo.

python start_app.py

echo.
echo Application stopped.
pause
